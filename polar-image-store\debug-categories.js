import { Polar } from '@polar-sh/sdk';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function debugCategories() {
  try {
    console.log('🔍 Debugging categories from Polar.sh...');
    
    const accessToken = process.env.POLAR_ACCESS_TOKEN;
    const organizationId = process.env.POLAR_ORGANIZATION_ID;
    
    if (!accessToken) {
      console.error('❌ POLAR_ACCESS_TOKEN not found');
      return;
    }
    
    if (!organizationId) {
      console.error('❌ POLAR_ORGANIZATION_ID not found');
      return;
    }
    
    console.log('✅ Environment variables found');
    console.log('📋 Organization ID:', organizationId);
    
    const polar = new Polar({
      accessToken,
      server: 'production'
    });
    
    console.log('🌐 Fetching products from Polar...');
    
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });
    
    const productList = response.result?.items || [];
    console.log(`📦 Found ${productList.length} products`);
    
    // Debug each product
    productList.forEach((product, index) => {
      console.log(`\n--- Product ${index + 1} ---`);
      console.log('ID:', product.id);
      console.log('Name:', product.name);
      console.log('Metadata:', JSON.stringify(product.metadata, null, 2));
      
      // Extract category from metadata
      let category = null;
      if (product.metadata) {
        // Look for category in metadata
        for (const [key, value] of Object.entries(product.metadata)) {
          if (key.toLowerCase().includes('category')) {
            category = value;
            console.log('📂 Category found in metadata:', category);
            break;
          }
        }
      }
      
      if (!category) {
        console.log('⚠️  No category found in metadata');
      }
    });
    
    // Extract categories using the same logic as the app
    const categories = productList
      .map(product => {
        if (product.metadata) {
          for (const [key, value] of Object.entries(product.metadata)) {
            if (key.toLowerCase().includes('category')) {
              return value;
            }
          }
        }
        return null;
      })
      .filter(Boolean)
      .filter((category, index, array) => array.indexOf(category) === index);
    
    console.log('\n🏷️  Unique categories found:', categories);
    console.log('📊 Total unique categories:', categories.length);
    
    // Check if "accessories" exists
    const hasAccessories = categories.includes('accessories');
    console.log('🔍 Has "accessories" category:', hasAccessories);
    
    if (!hasAccessories) {
      console.log('❌ "accessories" category not found in current products');
      console.log('💡 Available categories:', categories);
    }
    
  } catch (error) {
    console.error('❌ Error debugging categories:', error);
  }
}

debugCategories();
