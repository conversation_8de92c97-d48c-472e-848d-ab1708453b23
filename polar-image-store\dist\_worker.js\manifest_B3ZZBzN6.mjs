globalThis.process ??= {}; globalThis.process.env ??= {};
import { g as decodeKey } from './chunks/astro/server_2nS8CDxS.mjs';
import './chunks/astro-designed-error-pages_BIJyZyHZ.mjs';
import { N as NOOP_MIDDLEWARE_FN } from './chunks/noop-middleware_C1bQDL6T.mjs';

function sanitizeParams(params) {
  return Object.fromEntries(
    Object.entries(params).map(([key, value]) => {
      if (typeof value === "string") {
        return [key, value.normalize().replace(/#/g, "%23").replace(/\?/g, "%3F")];
      }
      return [key, value];
    })
  );
}
function getParameter(part, params) {
  if (part.spread) {
    return params[part.content.slice(3)] || "";
  }
  if (part.dynamic) {
    if (!params[part.content]) {
      throw new TypeError(`Missing parameter: ${part.content}`);
    }
    return params[part.content];
  }
  return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]");
}
function getSegment(segment, params) {
  const segmentPath = segment.map((part) => getParameter(part, params)).join("");
  return segmentPath ? "/" + segmentPath : "";
}
function getRouteGenerator(segments, addTrailingSlash) {
  return (params) => {
    const sanitizedParams = sanitizeParams(params);
    let trailing = "";
    if (addTrailingSlash === "always" && segments.length) {
      trailing = "/";
    }
    const path = segments.map((segment) => getSegment(segment, sanitizedParams)).join("") + trailing;
    return path || "/";
  };
}

function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0,
    fallbackRoutes: rawRouteData.fallbackRoutes.map((fallback) => {
      return deserializeRouteData(fallback);
    }),
    isIndex: rawRouteData.isIndex,
    origin: rawRouteData.origin
  };
}

function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const inlinedScripts = new Map(serializedManifest.inlinedScripts);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  const serverIslandNameMap = new Map(serializedManifest.serverIslandNameMap);
  const key = decodeKey(serializedManifest.key);
  return {
    // in case user middleware exists, this no-op middleware will be reassigned (see plugin-ssr.ts)
    middleware() {
      return { onRequest: NOOP_MIDDLEWARE_FN };
    },
    ...serializedManifest,
    assets,
    componentMetadata,
    inlinedScripts,
    clientDirectives,
    routes,
    serverIslandNameMap,
    key
  };
}

const manifest = deserializeManifest({"hrefRoot":"file:///D:/code/image/polar-image-store/","cacheDir":"file:///D:/code/image/polar-image-store/node_modules/.astro/","outDir":"file:///D:/code/image/polar-image-store/dist/","srcDir":"file:///D:/code/image/polar-image-store/src/","publicDir":"file:///D:/code/image/polar-image-store/public/","buildClientDir":"file:///D:/code/image/polar-image-store/dist/","buildServerDir":"file:///D:/code/image/polar-image-store/dist/_worker.js/","adapterName":"@astrojs/cloudflare","routes":[{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"page","component":"_server-islands.astro","params":["name"],"segments":[[{"content":"_server-islands","dynamic":false,"spread":false}],[{"content":"name","dynamic":true,"spread":false}]],"pattern":"^\\/_server-islands\\/([^/]+?)\\/?$","prerender":false,"isIndex":false,"fallbackRoutes":[],"route":"/_server-islands/[name]","origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"about/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/about","isIndex":false,"type":"page","pattern":"^\\/about\\/?$","segments":[[{"content":"about","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/about.astro","pathname":"/about","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"privacy/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/privacy","isIndex":false,"type":"page","pattern":"^\\/privacy\\/?$","segments":[[{"content":"privacy","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/privacy.astro","pathname":"/privacy","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"products/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/products","isIndex":true,"type":"page","pattern":"^\\/products\\/?$","segments":[[{"content":"products","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/products/index.astro","pathname":"/products","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"success/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/success","isIndex":false,"type":"page","pattern":"^\\/success\\/?$","segments":[[{"content":"success","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/success.astro","pathname":"/success","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"terms/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/terms","isIndex":false,"type":"page","pattern":"^\\/terms\\/?$","segments":[[{"content":"terms","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/terms.astro","pathname":"/terms","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/","isIndex":true,"type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"endpoint","isIndex":false,"route":"/_image","pattern":"^\\/_image\\/?$","segments":[[{"content":"_image","dynamic":false,"spread":false}]],"params":[],"component":"node_modules/@astrojs/cloudflare/dist/entrypoints/image-endpoint.js","pathname":"/_image","prerender":false,"fallbackRoutes":[],"origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/checkout","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/checkout\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"checkout","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/checkout.ts","pathname":"/api/checkout","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/products","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/products\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"products","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/products.ts","pathname":"/api/products","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/search","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/search\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"search","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/search.ts","pathname":"/api/search","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/webhooks","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/webhooks\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"webhooks","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/webhooks.ts","pathname":"/api/webhooks","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}}],"site":"https://infpik.store","base":"/","trailingSlash":"ignore","compressHTML":true,"componentMetadata":[["D:/code/image/polar-image-store/src/pages/about.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/index.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/privacy.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/[slug].astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/category/[category].astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/index.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/products/tag/[tag].astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/success.astro",{"propagation":"none","containsHead":true}],["D:/code/image/polar-image-store/src/pages/terms.astro",{"propagation":"none","containsHead":true}]],"renderers":[],"clientDirectives":[["idle","(()=>{var l=(n,t)=>{let i=async()=>{await(await n())()},e=typeof t.value==\"object\"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};\"requestIdleCallback\"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var n=(a,t)=>{let i=async()=>{await(await a())()};if(t.value){let e=matchMedia(t.value);e.matches?i():e.addEventListener(\"change\",i,{once:!0})}};(self.Astro||(self.Astro={})).media=n;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var a=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value==\"object\"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let l of e)if(l.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=a;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000@astrojs-ssr-adapter":"<EMAIL>","\u0000noop-actions":"_noop-actions.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000astro-internal:middleware":"_astro-internal_middleware.mjs","\u0000@astro-page:node_modules/@astrojs/cloudflare/dist/entrypoints/image-endpoint@_@js":"pages/_image.astro.mjs","\u0000@astro-page:src/pages/about@_@astro":"pages/about.astro.mjs","\u0000@astro-page:src/pages/api/checkout@_@ts":"pages/api/checkout.astro.mjs","\u0000@astro-page:src/pages/api/products@_@ts":"pages/api/products.astro.mjs","\u0000@astro-page:src/pages/api/search@_@ts":"pages/api/search.astro.mjs","\u0000@astro-page:src/pages/api/webhooks@_@ts":"pages/api/webhooks.astro.mjs","\u0000@astro-page:src/pages/privacy@_@astro":"pages/privacy.astro.mjs","\u0000@astro-page:src/pages/products/category/[category]@_@astro":"pages/products/category/_category_.astro.mjs","\u0000@astro-page:src/pages/products/index@_@astro":"pages/products.astro.mjs","\u0000@astro-page:src/pages/success@_@astro":"pages/success.astro.mjs","\u0000@astro-page:src/pages/terms@_@astro":"pages/terms.astro.mjs","\u0000@astrojs-ssr-virtual-entry":"index.js","\u0000@astro-page:src/pages/products/tag/[tag]@_@astro":"pages/products/tag/_tag_.astro.mjs","\u0000@astro-page:src/pages/products/[slug]@_@astro":"pages/products/_slug_.astro.mjs","\u0000@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","D:/code/image/polar-image-store/node_modules/astro/dist/assets/services/sharp.js":"chunks/sharp_BEV-KcfX.mjs","D:/code/image/polar-image-store/node_modules/unstorage/drivers/cloudflare-kv-binding.mjs":"chunks/cloudflare-kv-binding_DMly_2Gl.mjs","\u0000@astrojs-manifest":"manifest_B3ZZBzN6.mjs","D:/code/image/polar-image-store/src/pages/products/category/[category].astro?astro&type=script&index=0&lang.ts":"_astro/_category_.astro_astro_type_script_index_0_lang.D1aru-wX.js","D:/code/image/polar-image-store/src/pages/products/tag/[tag].astro?astro&type=script&index=0&lang.ts":"_astro/_tag_.astro_astro_type_script_index_0_lang.vuWLXbTY.js","D:/code/image/polar-image-store/src/pages/products/index.astro?astro&type=script&index=0&lang.ts":"_astro/index.astro_astro_type_script_index_0_lang.DRWsYfmL.js","D:/code/image/polar-image-store/src/pages/index.astro?astro&type=script&index=0&lang.ts":"_astro/index.astro_astro_type_script_index_0_lang._Svs-xa0.js","D:/code/image/polar-image-store/src/layouts/Layout.astro?astro&type=script&index=0&lang.ts":"_astro/Layout.astro_astro_type_script_index_0_lang.DADd3tG8.js","D:/code/image/polar-image-store/src/components/CategoryNavigation.astro?astro&type=script&index=0&lang.ts":"_astro/CategoryNavigation.astro_astro_type_script_index_0_lang.CyyS81NA.js","D:/code/image/polar-image-store/src/components/Hero.astro?astro&type=script&index=0&lang.ts":"_astro/Hero.astro_astro_type_script_index_0_lang.tzVoaVG-.js","D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts":"_astro/ImageGallery.astro_astro_type_script_index_0_lang.dQDmp6mo.js","D:/code/image/polar-image-store/src/components/TagNavigation.astro?astro&type=script&index=0&lang.ts":"_astro/TagNavigation.astro_astro_type_script_index_0_lang.CR32wsAC.js","astro:scripts/before-hydration.js":""},"inlinedScripts":[["D:/code/image/polar-image-store/src/pages/products/category/[category].astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{window.addEventListener(\"categoryChange\",o=>{const e=o.detail.categoryId;e===\"all\"?window.location.href=\"/products\":window.location.href=`/products/category/${e}`})});"],["D:/code/image/polar-image-store/src/pages/products/tag/[tag].astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{window.addEventListener(\"tagChange\",d=>{const t=d.detail.tagId;t===\"all\"?window.location.href=\"/products\":window.location.href=`/products/tag/${t}`})});"],["D:/code/image/polar-image-store/src/pages/products/index.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{const d=new URLSearchParams(window.location.search),y=d.get(\"category\")||\"all\",u=d.get(\"search\")||\"\";l(y,u),window.addEventListener(\"categoryChange\",s=>{const t=s.detail.categoryId,e=new URL(window.location);t===\"all\"?e.searchParams.delete(\"category\"):e.searchParams.set(\"category\",t),window.history.pushState({},\"\",e.toString());const o=e.searchParams.get(\"search\")||\"\";l(t,o)});function l(s,t){const e=document.querySelectorAll(\".product-item\");let o=0;e.forEach(a=>{const g=a.dataset.category,p=a.dataset.name,h=a.dataset.description,m=a.dataset.tags;let w=s===\"all\"||g===s,i=!0;if(t.trim()){const n=t.toLowerCase();i=p.includes(n)||h.includes(n)||m.includes(n)}w&&i?(a.style.display=\"block\",o++):a.style.display=\"none\"});const c=document.querySelector(\".empty-state\"),r=document.getElementById(\"products-grid\");o===0?(r&&(r.style.display=\"none\"),c&&(c.style.display=\"block\")):(r&&(r.style.display=\"grid\"),c&&(c.style.display=\"none\"))}});"],["D:/code/image/polar-image-store/src/pages/index.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{window.addEventListener(\"categoryChange\",e=>{const o=e.detail.categoryId;l(o)});function l(e){const o=document.querySelectorAll(\".product-item\");o.forEach(t=>{const r=t.dataset.category;parseInt(t.dataset.index),e===\"all\"||r===e?t.style.display=\"block\":t.style.display=\"none\"}),e===\"all\"&&o.forEach(t=>{parseInt(t.dataset.index)>=16&&(t.style.display=\"none\")});const n=document.querySelector('a[href=\"/products\"].btn-primary');if(n&&e!==\"all\"){const t=s(e);n.innerHTML=`\n          View All ${t} Products\n          <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n          </svg>\n        `,n.href=`/products/category/${e}`}else n&&e===\"all\"&&(n.innerHTML=`\n          View All Products\n          <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n          </svg>\n        `,n.href=\"/products\")}function s(e){return e.split(\"-\").map(o=>o.charAt(0).toUpperCase()+o.slice(1)).join(\" \")}});"],["D:/code/image/polar-image-store/src/layouts/Layout.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{const o=document.getElementById(\"mobile-menu-button\"),c=document.getElementById(\"mobile-menu\");o&&c&&o.addEventListener(\"click\",()=>{c.classList.toggle(\"hidden\")});const i=document.getElementById(\"productSearch\"),d=document.getElementById(\"mobileProductSearch\"),e=document.getElementById(\"searchResults\");let a;async function m(t){const s=t.value.trim();a&&clearTimeout(a),s.length>2?e&&(e.classList.remove(\"hidden\"),e.innerHTML=`\n            <div class=\"p-4 text-center text-primary-600\">\n              <div class=\"flex items-center justify-center gap-2\">\n                <svg class=\"animate-spin w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                </svg>\n                <span class=\"text-sm\">Searching for \"${s}\"...</span>\n              </div>\n            </div>\n          `,a=setTimeout(async()=>{try{const r=await(await fetch(`/api/search?q=${encodeURIComponent(s)}`)).json();if(e&&!e.classList.contains(\"hidden\"))if(r.results&&r.results.length>0){const u=r.results.map(n=>`\n                    <a href=\"/products/${n.slug}\" class=\"block p-3 hover:bg-primary-50 rounded-lg transition-colors border-b border-primary-100 last:border-b-0\">\n                      <div class=\"flex items-center gap-3\">\n                        ${n.image?`<img src=\"${n.image}\" alt=\"${n.name}\" class=\"w-10 h-10 object-cover rounded-lg\">`:\"\"}\n                        <div class=\"flex-1 min-w-0\">\n                          <div class=\"text-primary-900 font-medium truncate\">${n.name}</div>\n                          <div class=\"text-primary-600 text-sm truncate\">${n.description}</div>\n                          <div class=\"text-accent-600 text-sm font-semibold\">$${n.price}</div>\n                        </div>\n                      </div>\n                    </a>\n                  `).join(\"\");e.innerHTML=`\n                    <div class=\"p-2\">\n                      <div class=\"text-xs text-primary-500 px-3 py-2 border-b border-primary-100\">\n                        Found ${r.total} result${r.total===1?\"\":\"s\"}\n                      </div>\n                      ${u}\n                      <div class=\"p-3 border-t border-primary-100\">\n                        <a href=\"/products?search=${encodeURIComponent(s)}\" class=\"block text-center text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors\">\n                          View all ${r.total} results →\n                        </a>\n                      </div>\n                    </div>\n                  `}else e.innerHTML=`\n                    <div class=\"p-4 text-center\">\n                      <div class=\"text-primary-600 mb-2\">No results found for \"${s}\"</div>\n                      <a href=\"/products\" class=\"text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors\">\n                        Browse all products →\n                      </a>\n                    </div>\n                  `}catch(l){console.error(\"Search error:\",l),e&&!e.classList.contains(\"hidden\")&&(e.innerHTML=`\n                  <div class=\"p-4 text-center text-red-600\">\n                    <div class=\"text-sm\">Search failed. Please try again.</div>\n                  </div>\n                `)}},300)):e&&e.classList.add(\"hidden\")}i&&(i.addEventListener(\"input\",t=>m(t.target)),i.addEventListener(\"keydown\",t=>{if(t.key===\"Enter\"){t.preventDefault();const s=t.target.value.trim();s&&(window.location.href=`/products?search=${encodeURIComponent(s)}`)}})),d&&d.addEventListener(\"keydown\",t=>{if(t.key===\"Enter\"){t.preventDefault();const s=t.target.value.trim();s&&(window.location.href=`/products?search=${encodeURIComponent(s)}`)}}),document.addEventListener(\"click\",t=>{e&&!i?.contains(t.target)&&!e.contains(t.target)&&e.classList.add(\"hidden\")})});"],["D:/code/image/polar-image-store/src/components/CategoryNavigation.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{const s=document.getElementById(\"categoryScroll\"),e=document.getElementById(\"scrollLeft\"),n=document.getElementById(\"scrollRight\"),a=document.querySelectorAll(\".category-tab\");if(!s||!e||!n)return;function r(){const{scrollLeft:o,scrollWidth:t,clientWidth:c}=s;t>c?(e.style.opacity=o>0?\"1\":\"0\",e.style.pointerEvents=o>0?\"auto\":\"none\",n.style.opacity=o<t-c?\"1\":\"0\",n.style.pointerEvents=o<t-c?\"auto\":\"none\"):(e.style.opacity=\"0\",e.style.pointerEvents=\"none\",n.style.opacity=\"0\",n.style.pointerEvents=\"none\")}e.addEventListener(\"click\",()=>{s.scrollBy({left:-200,behavior:\"smooth\"})}),n.addEventListener(\"click\",()=>{s.scrollBy({left:200,behavior:\"smooth\"})}),s.addEventListener(\"scroll\",r),window.addEventListener(\"resize\",r),r(),a.forEach(o=>{o.addEventListener(\"click\",t=>{const c=t.currentTarget.dataset.category;a.forEach(l=>{l.classList.remove(\"bg-accent-600\",\"text-white\",\"shadow-md\"),l.classList.add(\"bg-primary-50\",\"text-primary-700\")}),t.currentTarget.classList.remove(\"bg-primary-50\",\"text-primary-700\"),t.currentTarget.classList.add(\"bg-accent-600\",\"text-white\",\"shadow-md\"),console.log(\"📡 Dispatching categoryChange event:\",c),window.dispatchEvent(new CustomEvent(\"categoryChange\",{detail:{categoryId:c}}))})})});"],["D:/code/image/polar-image-store/src/components/Hero.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{const e=document.getElementById(\"categoryScroll\");if(e){let a=!1,s,c;e.addEventListener(\"touchstart\",t=>{a=!0,s=t.touches[0].pageX-e.offsetLeft,c=e.scrollLeft}),e.addEventListener(\"touchend\",()=>{a=!1}),e.addEventListener(\"touchmove\",t=>{if(!a)return;const r=(t.touches[0].pageX-e.offsetLeft-s)*2;e.scrollLeft=c-r}),e.addEventListener(\"mousedown\",t=>{a=!0,s=t.pageX-e.offsetLeft,c=e.scrollLeft,e.style.cursor=\"grabbing\"}),e.addEventListener(\"mouseleave\",()=>{a=!1,e.style.cursor=\"grab\"}),e.addEventListener(\"mouseup\",()=>{a=!1,e.style.cursor=\"grab\"}),e.addEventListener(\"mousemove\",t=>{if(!a)return;t.preventDefault();const r=(t.pageX-e.offsetLeft-s)*2;e.scrollLeft=c-r}),e.style.cursor=\"grab\";const d=document.querySelectorAll(\".category-tab\");d.forEach(t=>{t.addEventListener(\"click\",o=>{const r=o.currentTarget.dataset.category;d.forEach(i=>{i.classList.remove(\"bg-accent-600\",\"text-white\",\"shadow-md\"),i.classList.add(\"bg-primary-50\",\"text-primary-700\",\"hover:bg-primary-100\",\"hover:text-primary-900\");const l=i.querySelector(\"span\");l&&(l.classList.remove(\"bg-white/20\",\"text-white\"),l.classList.add(\"bg-primary-200\",\"text-primary-600\"))}),t.classList.remove(\"bg-primary-50\",\"text-primary-700\",\"hover:bg-primary-100\",\"hover:text-primary-900\"),t.classList.add(\"bg-accent-600\",\"text-white\",\"shadow-md\");const n=t.querySelector(\"span\");n&&(n.classList.remove(\"bg-primary-200\",\"text-primary-600\"),n.classList.add(\"bg-white/20\",\"text-white\")),window.location.pathname===\"/\"?(console.log(\"📡 Dispatching categoryChange event for homepage:\",r),window.dispatchEvent(new CustomEvent(\"categoryChange\",{detail:{categoryId:r}}))):r===\"all\"?window.location.href=\"/products\":window.location.href=`/products/category/${r}`})}),document.querySelectorAll(\".tag-tab\").forEach(t=>{t.addEventListener(\"click\",o=>{const r=o.currentTarget.dataset.tag;r===\"all\"?window.location.href=\"/products\":window.location.href=`/products/tag/${r}`})})}});"],["D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts","let o=0,n=[];document.addEventListener(\"DOMContentLoaded\",function(){const e=document.querySelectorAll(\".thumbnail img\");if(n=Array.from(e).map(t=>t.src),n.length===0){const t=document.getElementById(\"mainImage\");t&&(n=[t.src])}});function c(){const e=document.getElementById(\"lightbox\");e&&(e.classList.remove(\"active\"),document.body.style.overflow=\"auto\")}function g(){n.length>1&&(o=(o-1+n.length)%n.length,i())}function a(){n.length>1&&(o=(o+1)%n.length,i())}function i(){const e=document.getElementById(\"lightboxImage\"),t=document.getElementById(\"imageCounter\");e&&t&&(e.src=n[o],e.alt=`Image ${o+1}`,t.textContent=`${o+1} / ${n.length}`)}document.addEventListener(\"keydown\",function(e){const t=document.getElementById(\"lightbox\");if(t&&t.classList.contains(\"active\"))switch(e.key){case\"Escape\":c();break;case\"ArrowLeft\":g();break;case\"ArrowRight\":a();break}});document.addEventListener(\"click\",function(e){const t=document.getElementById(\"lightbox\");e.target===t&&c()});"],["D:/code/image/polar-image-store/src/components/TagNavigation.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",()=>{const e=document.getElementById(\"tagScroll\"),l=document.getElementById(\"leftFade\"),c=document.getElementById(\"rightFade\");if(e&&l&&c){let d=function(){const{scrollLeft:t,scrollWidth:r,clientWidth:a}=e;l.style.opacity=t>0?\"1\":\"0\",c.style.opacity=t<r-a?\"1\":\"0\"};d(),e.addEventListener(\"scroll\",d),window.addEventListener(\"resize\",d);let s=!1,o,n;e.addEventListener(\"touchstart\",t=>{s=!0,o=t.touches[0].pageX-e.offsetLeft,n=e.scrollLeft}),e.addEventListener(\"touchend\",()=>{s=!1}),e.addEventListener(\"touchmove\",t=>{if(!s)return;const a=(t.touches[0].pageX-e.offsetLeft-o)*2;e.scrollLeft=n-a}),e.addEventListener(\"mousedown\",t=>{s=!0,o=t.pageX-e.offsetLeft,n=e.scrollLeft,e.style.cursor=\"grabbing\"}),e.addEventListener(\"mouseleave\",()=>{s=!1,e.style.cursor=\"grab\"}),e.addEventListener(\"mouseup\",()=>{s=!1,e.style.cursor=\"grab\"}),e.addEventListener(\"mousemove\",t=>{if(!s)return;t.preventDefault();const a=(t.pageX-e.offsetLeft-o)*2;e.scrollLeft=n-a}),e.style.cursor=\"grab\";const i=document.querySelectorAll(\".tag-tab\");i.forEach(t=>{t.addEventListener(\"click\",r=>{const a=r.currentTarget.dataset.tag;i.forEach(u=>{u.classList.remove(\"bg-blue-600\",\"text-white\",\"shadow-md\"),u.classList.add(\"bg-white\",\"text-gray-700\",\"border\",\"border-gray-300\")}),r.currentTarget.classList.remove(\"bg-white\",\"text-gray-700\",\"border\",\"border-gray-300\"),r.currentTarget.classList.add(\"bg-blue-600\",\"text-white\",\"shadow-md\"),window.dispatchEvent(new CustomEvent(\"tagChange\",{detail:{tagId:a}}))})})}});"]],"assets":["/_astro/about.BGziO9gN.css","/favicon.svg","/logo.svg","/og-image.jpg","/placeholder-image.svg","/robots.txt","/_worker.js/index.js","/_worker.js/renderers.mjs","/_worker.js/<EMAIL>","/_worker.js/_astro-internal_middleware.mjs","/_worker.js/_noop-actions.mjs","/_worker.js/chunks/astro-designed-error-pages_BIJyZyHZ.mjs","/_worker.js/chunks/astro_BjNfVcL8.mjs","/_worker.js/chunks/CategoryNavigation_CrRWqrBR.mjs","/_worker.js/chunks/cloudflare-kv-binding_DMly_2Gl.mjs","/_worker.js/chunks/index_C4LTQoxk.mjs","/_worker.js/chunks/index_CzP--SSp.mjs","/_worker.js/chunks/Layout_GsFfzzTL.mjs","/_worker.js/chunks/noop-middleware_C1bQDL6T.mjs","/_worker.js/chunks/path_h5kZAkfu.mjs","/_worker.js/chunks/polar_6yRTsV5G.mjs","/_worker.js/chunks/ProductCard_WYIug3K0.mjs","/_worker.js/chunks/sdk_DEQ9AU5A.mjs","/_worker.js/chunks/server_BulHO2l4.mjs","/_worker.js/chunks/sharp_BEV-KcfX.mjs","/_worker.js/chunks/StructuredData_CFhLJGM8.mjs","/_worker.js/pages/about.astro.mjs","/_worker.js/pages/index.astro.mjs","/_worker.js/pages/privacy.astro.mjs","/_worker.js/pages/products.astro.mjs","/_worker.js/pages/success.astro.mjs","/_worker.js/pages/terms.astro.mjs","/_worker.js/pages/_image.astro.mjs","/_worker.js/_astro/about.BGziO9gN.css","/_worker.js/pages/api/checkout.astro.mjs","/_worker.js/pages/api/products.astro.mjs","/_worker.js/pages/api/search.astro.mjs","/_worker.js/pages/api/webhooks.astro.mjs","/_worker.js/chunks/astro/server_2nS8CDxS.mjs","/_worker.js/pages/products/_slug_.astro.mjs","/_worker.js/pages/products/tag/_tag_.astro.mjs","/_worker.js/pages/products/category/_category_.astro.mjs","/about/index.html","/privacy/index.html","/products/index.html","/success/index.html","/terms/index.html","/index.html"],"buildFormat":"directory","checkOrigin":true,"serverIslandNameMap":[],"key":"kxHbaRl+005F1cqkVl2WNmIEn7BLf7OFrju1F8Oib9o=","sessionConfig":{"driver":"cloudflare-kv-binding","options":{"binding":"SESSION"}}});
if (manifest.sessionConfig) manifest.sessionConfig.driverModule = () => import('./chunks/cloudflare-kv-binding_DMly_2Gl.mjs');

export { manifest };
