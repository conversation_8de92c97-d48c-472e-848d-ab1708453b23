globalThis.process ??= {}; globalThis.process.env ??= {};
import './chunks/astro-designed-error-pages_YV2M0Any.mjs';
import './chunks/astro/server_2nS8CDxS.mjs';
import { s as sequence } from './chunks/index_BqEKIj69.mjs';

const onRequest$1 = (context, next) => {
  if (context.isPrerendered) {
    context.locals.runtime ??= {
      env: process.env
    };
  }
  return next();
};

const onRequest = sequence(
	onRequest$1,
	
	
);

export { onRequest };
