<!DOCTYPE html><html lang="en"> <head><title>Polar Image Store - Premium Digital Images & Artwork</title><meta charset="UTF-8"><link rel="canonical" href="https://infpik.store/"><meta name="description" content="Discover premium digital images and artwork for your creative projects. High-quality, commercial-use digital assets available for instant download."><meta name="robots" content="index, follow"><meta property="og:title" content="Polar Image Store - Premium Digital Images &#38; Artwork"><meta property="og:type" content="website"><meta property="og:image" content="http://infpik.store/og-image.jpg"><meta property="og:url" content="https://infpik.store/"><meta property="og:description" content="Discover premium digital images and artwork for your creative projects. High-quality, commercial-use digital assets available for instant download."><meta property="og:locale" content="en_US"><meta property="og:site_name" content="Polar Image Store"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:site" content="@polarimagestore"><meta name="twitter:title" content="Polar Image Store - Premium Digital Images &#38; Artwork"><meta name="twitter:image" content="http://infpik.store/og-image.jpg"><meta name="twitter:image:alt" content="Polar Image Store - Premium Digital Images &#38; Artwork - Polar Image Store"><meta name="twitter:description" content="Discover premium digital images and artwork for your creative projects. High-quality, commercial-use digital assets available for instant download."><meta name="twitter:creator" content="@polarimagestore"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><link rel="sitemap" href="/sitemap-index.xml"><link rel="canonical" href="https://infpik.store/"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="Astro v5.12.4"><meta name="robots" content="index, follow"><meta name="googlebot" content="index, follow"><meta name="theme-color" content="#6366f1"><meta name="msapplication-TileColor" content="#6366f1"><link rel="stylesheet" href="/_astro/about.BGziO9gN.css">
<style>.bg-grid-pattern[data-astro-cid-bbe6dxrz]{background-image:linear-gradient(rgba(0,0,0,.1) 1px,transparent 1px),linear-gradient(90deg,rgba(0,0,0,.1) 1px,transparent 1px);background-size:20px 20px}.scrollbar-hide[data-astro-cid-bbe6dxrz]{-ms-overflow-style:none;scrollbar-width:none}.scrollbar-hide[data-astro-cid-bbe6dxrz]::-webkit-scrollbar{display:none}
</style></head> <body class="min-h-screen flex flex-col"> <header class="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-primary-100 py-4"> <div class="container"> <nav class="flex items-center justify-between"> <!-- Logo --> <a href="/" class="flex items-center gap-2 text-xl font-bold text-primary-900 hover:text-accent-600 transition-colors"> <img src="/logo.svg" alt="Logo" loading="lazy" decoding="async" fetchpriority="auto" width="32" height="32" class="w-8 h-8 text-accent-600">
InfPik
</a> <!-- Mobile Search Bar --> <div class="md:hidden relative flex-1 mx-2"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="mobileProductSearch" class="block w-full pl-8 pr-4 py-1.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 text-sm" placeholder="Search products..." autocomplete="off"> </div> <!-- Search Bar (Desktop) --> <div class="hidden md:flex flex-1 max-w-md mx-8"> <div class="relative w-full"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="productSearch" class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200" placeholder="Search products..." autocomplete="off"> <!-- Search results dropdown (hidden by default) --> <div id="searchResults" class="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 hidden max-h-96 overflow-y-auto"> <!-- Search results will be populated here --> </div> </div> </div> <!-- CTA Button & Mobile Menu --> <div class="flex items-center gap-4"> <a href="/products" class="btn-primary hidden md:inline-flex">
Browse Collection
</a> <!-- Mobile menu button --> <button class="md:hidden p-2 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full transition-all" id="mobile-menu-button"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> </button> </div> </nav> <!-- Mobile menu --> <div class="md:hidden hidden" id="mobile-menu"> <div class="pt-4 pb-2 border-t border-primary-100 mt-4"> <!-- Mobile Navigation --> <ul class="space-y-2"> <li><a href="/" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Home</a></li> <li><a href="/products" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Products</a></li> <li><a href="/about" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">About</a></li> </ul> <!-- Legal Links --> <div class="mt-4 pt-4 border-t border-primary-100"> <p class="px-4 text-xs uppercase text-primary-500 font-medium mb-2">Legal</p> <ul class="space-y-2"> <li><a href="/privacy" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Privacy Policy</a></li> <li><a href="/terms" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Terms of Service</a></li> </ul> </div> <div class="mt-4 pt-4 border-t border-primary-100"> <a href="/products" class="btn-primary w-full justify-center">
Browse Collection
</a> </div> </div> </div> </div> </header> <main class="flex-1 pb-12">   <script type="application/ld+json">{"@context":"https://schema.org","@type":"Organization","name":"Polar Image Store","url":"https://infpik.store","logo":"https://infpik.store/favicon.svg","description":"Premium digital images and artwork for creative projects","sameAs":["https://twitter.com/polarimagestore","https://facebook.com/polarimagestore"],"contactPoint":{"@type":"ContactPoint","contactType":"customer service","email":"<EMAIL>"}}</script>  <script type="application/ld+json">{"@context":"https://schema.org","@type":"WebSite","name":"Polar Image Store","url":"https://infpik.store","description":"Premium digital images and artwork for creative projects","publisher":{"@type":"Organization","name":"Polar Image Store"},"potentialAction":{"@type":"SearchAction","target":"https://infpik.store/products?search={search_term_string}","query-input":"required name=search_term_string"}}</script>  <section class="relative py-12 lg:py-20 bg-gradient-to-br from-white via-primary-50/30 to-accent-50/20 overflow-hidden" data-astro-cid-bbe6dxrz> <!-- Background decoration --> <div class="absolute inset-0 bg-grid-pattern opacity-5" data-astro-cid-bbe6dxrz></div> <div class="absolute top-20 right-20 w-72 h-72 bg-accent-200/20 rounded-full blur-3xl" data-astro-cid-bbe6dxrz></div> <div class="absolute bottom-20 left-20 w-96 h-96 bg-primary-200/20 rounded-full blur-3xl" data-astro-cid-bbe6dxrz></div> <div class="container relative" data-astro-cid-bbe6dxrz> <div class="max-w-4xl mx-auto" data-astro-cid-bbe6dxrz> <!-- 1. Main title --> <h1 class="text-3xl md:text-4xl lg:text-5xl xl:text-5xl font-bold text-primary-900 mb-8 leading-tight max-w-3xl text-center" data-astro-cid-bbe6dxrz>Premium. Passion. Creativity.</h1> <!-- 2. Category Navigation --> <div class="relative mb-8" data-astro-cid-bbe6dxrz> <div class="overflow-x-auto scrollbar-hide" id="categoryScroll" data-astro-cid-bbe6dxrz> <div class="flex gap-2 pb-2 min-w-max justify-center" data-astro-cid-bbe6dxrz> <button class="category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap bg-accent-600 text-white shadow-md" data-category="all"> All <span class="text-xs px-2 py-0.5 rounded-full bg-white/20 text-white"> 2 </span> </button><button class="category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap bg-primary-50 text-primary-700 hover:bg-primary-100 hover:text-primary-900" data-category="accessories"> Accessories <span class="text-xs px-2 py-0.5 rounded-full bg-primary-200 text-primary-600"> 1 </span> </button><button class="category-tab flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all whitespace-nowrap bg-primary-50 text-primary-700 hover:bg-primary-100 hover:text-primary-900" data-category="food"> Food <span class="text-xs px-2 py-0.5 rounded-full bg-primary-200 text-primary-600"> 1 </span> </button> </div> </div> </div> <!-- 3. Search Bar --> <div class="relative mb-8" data-astro-cid-bbe6dxrz> <div class="max-w-md mx-auto" data-astro-cid-bbe6dxrz> <form action="/products" method="GET" class="relative" data-astro-cid-bbe6dxrz> <input type="text" name="search" placeholder="Search products..." class="w-full px-4 py-3 pl-12 pr-4 text-gray-900 bg-white border border-gray-300 rounded-full shadow-sm focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all" data-astro-cid-bbe6dxrz> <div class="absolute inset-y-0 left-0 flex items-center pl-4" data-astro-cid-bbe6dxrz> <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-bbe6dxrz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" data-astro-cid-bbe6dxrz></path> </svg> </div> <button type="submit" class="absolute inset-y-0 right-0 flex items-center pr-3" data-astro-cid-bbe6dxrz> <span class="sr-only" data-astro-cid-bbe6dxrz>Search</span> </button> </form> </div> </div> <!-- 4. Tag Navigation --> <div class="relative" data-astro-cid-bbe6dxrz>  <div class="text-center"> <h3 class="text-lg font-semibold text-gray-900 mb-4">Popular Tags</h3> <div class="flex flex-wrap gap-2 justify-center max-w-3xl mx-auto"> <button class="tag-tab inline-flex items-center gap-1.5 px-3 py-1.5 bg-white text-gray-700 border border-gray-300 rounded-full text-xs font-medium transition-all hover:bg-gray-100 hover:text-gray-900" data-tag="headphone">
#Headphone <span class="text-xs px-1.5 py-0.5 bg-gray-200 text-gray-600 rounded-full"> 1 </span> </button><button class="tag-tab inline-flex items-center gap-1.5 px-3 py-1.5 bg-white text-gray-700 border border-gray-300 rounded-full text-xs font-medium transition-all hover:bg-gray-100 hover:text-gray-900" data-tag="sushi">
#Sushi <span class="text-xs px-1.5 py-0.5 bg-gray-200 text-gray-600 rounded-full"> 1 </span> </button> </div> </div>  </div> </div> </div> </section>  <script type="module">document.addEventListener("DOMContentLoaded",()=>{const e=document.getElementById("categoryScroll");if(e){let a=!1,s,c;e.addEventListener("touchstart",t=>{a=!0,s=t.touches[0].pageX-e.offsetLeft,c=e.scrollLeft}),e.addEventListener("touchend",()=>{a=!1}),e.addEventListener("touchmove",t=>{if(!a)return;const r=(t.touches[0].pageX-e.offsetLeft-s)*2;e.scrollLeft=c-r}),e.addEventListener("mousedown",t=>{a=!0,s=t.pageX-e.offsetLeft,c=e.scrollLeft,e.style.cursor="grabbing"}),e.addEventListener("mouseleave",()=>{a=!1,e.style.cursor="grab"}),e.addEventListener("mouseup",()=>{a=!1,e.style.cursor="grab"}),e.addEventListener("mousemove",t=>{if(!a)return;t.preventDefault();const r=(t.pageX-e.offsetLeft-s)*2;e.scrollLeft=c-r}),e.style.cursor="grab";const d=document.querySelectorAll(".category-tab");d.forEach(t=>{t.addEventListener("click",o=>{const r=o.currentTarget.dataset.category;d.forEach(i=>{i.classList.remove("bg-accent-600","text-white","shadow-md"),i.classList.add("bg-primary-50","text-primary-700","hover:bg-primary-100","hover:text-primary-900");const l=i.querySelector("span");l&&(l.classList.remove("bg-white/20","text-white"),l.classList.add("bg-primary-200","text-primary-600"))}),t.classList.remove("bg-primary-50","text-primary-700","hover:bg-primary-100","hover:text-primary-900"),t.classList.add("bg-accent-600","text-white","shadow-md");const n=t.querySelector("span");n&&(n.classList.remove("bg-primary-200","text-primary-600"),n.classList.add("bg-white/20","text-white")),window.location.pathname==="/"?(console.log("📡 Dispatching categoryChange event for homepage:",r),window.dispatchEvent(new CustomEvent("categoryChange",{detail:{categoryId:r}}))):r==="all"?window.location.href="/products":window.location.href=`/products/category/${r}`})}),document.querySelectorAll(".tag-tab").forEach(t=>{t.addEventListener("click",o=>{const r=o.currentTarget.dataset.tag;r==="all"?window.location.href="/products":window.location.href=`/products/tag/${r}`})})}});</script>  <section class="py-16 bg-white"> <div class="container"> <div class="text-center mb-12"> <h2 class="text-3xl md:text-4xl font-bold text-primary-900 mb-4">Featured Collections</h2> <p class="text-lg text-primary-600 max-w-2xl mx-auto">
Discover our most popular digital images and artwork, carefully curated for quality and creativity
</p> </div> <div id="products-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"> <div class="product-item" data-category="food" data-name="3d sushi icon" data-description="3d illustration sushi icon gives your project awesome illustration, this icon you can use for ui ux design, mobile apps, web infographics, and many more." data-tags="sushi" style data-index="0"> <div class="group bg-white rounded-3xl overflow-hidden shadow-sm border border-primary-100 transition-all duration-500 hover:-translate-y-3 hover:shadow-2xl hover:shadow-primary-500/10"> <div class="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-primary-50 to-accent-50"> <img src="https://polar-public-files.s3.amazonaws.com/product_media/e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca/d16336bf-f6c4-4780-9a7a-d2aa54e13fe2/3d%20sushi%20icon.png" alt="3D Sushi Icon" loading="eager" fetchpriority="high" decoding="async" width="800" height="600" class="w-full h-full object-cover transition-all duration-500 group-hover:scale-110"> <!-- Gradient overlay --> <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> <!-- Hover actions --> <div class="absolute inset-0 flex items-center justify-center gap-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0"> <a href="/products/3d-sushi-icon" class="flex items-center gap-2 px-6 py-3 bg-white/95 backdrop-blur-sm text-primary-900 rounded-full font-semibold text-sm transition-all duration-200 hover:bg-white hover:scale-105 hover:shadow-lg"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path> </svg>
Preview
</a> <a href="/api/checkout?products=afbbee5b-40fe-43e6-b01e-b82a90e5867d" class="flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold text-sm transition-all duration-200 hover:bg-accent-700 hover:scale-105 hover:shadow-lg"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path> </svg>
Buy Now
</a> </div> <!-- Price badge --> <div class="absolute top-4 right-4 px-3 py-1.5 bg-white/95 backdrop-blur-sm rounded-full"> <span class="text-lg font-bold text-primary-900">$1.00</span> </div> </div> <div class="p-6"> <!-- Category badge --> <div class="mb-3"> <span class="inline-flex items-center px-3 py-1 bg-accent-100 text-accent-700 text-xs font-medium rounded-full"> sushi </span> </div> <!-- Title --> <h3 class="text-xl font-bold text-primary-900 mb-2 line-clamp-2 group-hover:text-accent-600 transition-colors"> 3D Sushi Icon </h3> <!-- Description --> <p class="text-primary-600 text-sm mb-4 line-clamp-2 leading-relaxed"> 3D Illustration Sushi Icon gives your project awesome illustration, this icon you can use for UI UX design, mobile apps, web infographics, and many more. </p> <!-- Footer --> <div class="flex items-center justify-between pt-4 border-t border-primary-100"> <div class="flex items-center gap-2 text-xs text-primary-500"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path> </svg> <span>Digital Download</span> </div> <a href="/products/3d-sushi-icon" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
View Details →
</a> </div> </div> </div> </div><div class="product-item" data-category="accessories" data-name="3d in-ear headphone icon" data-description="3d illustration in-ear headphone icon gives your project awesome illustration, this icon you can use for ui ux design, mobile apps, web infographics, and many more." data-tags="headphone" style data-index="1"> <div class="group bg-white rounded-3xl overflow-hidden shadow-sm border border-primary-100 transition-all duration-500 hover:-translate-y-3 hover:shadow-2xl hover:shadow-primary-500/10"> <div class="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-primary-50 to-accent-50"> <img src="https://polar-public-files.s3.amazonaws.com/product_media/e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca/e2f653ac-d47f-4955-a077-9def34234897/3d%20in-ear%20icon.png" alt="3D in-ear headphone icon" loading="eager" fetchpriority="high" decoding="async" width="800" height="600" class="w-full h-full object-cover transition-all duration-500 group-hover:scale-110"> <!-- Gradient overlay --> <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> <!-- Hover actions --> <div class="absolute inset-0 flex items-center justify-center gap-3 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0"> <a href="/products/3d-in-ear-headphone-icon" class="flex items-center gap-2 px-6 py-3 bg-white/95 backdrop-blur-sm text-primary-900 rounded-full font-semibold text-sm transition-all duration-200 hover:bg-white hover:scale-105 hover:shadow-lg"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path> </svg>
Preview
</a> <a href="/api/checkout?products=a5955a3d-8e30-4855-ad8a-0bec98f30a6a" class="flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold text-sm transition-all duration-200 hover:bg-accent-700 hover:scale-105 hover:shadow-lg"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path> </svg>
Buy Now
</a> </div> <!-- Price badge --> <div class="absolute top-4 right-4 px-3 py-1.5 bg-white/95 backdrop-blur-sm rounded-full"> <span class="text-lg font-bold text-primary-900">$1.00</span> </div> </div> <div class="p-6"> <!-- Category badge --> <div class="mb-3"> <span class="inline-flex items-center px-3 py-1 bg-accent-100 text-accent-700 text-xs font-medium rounded-full"> headphone </span> </div> <!-- Title --> <h3 class="text-xl font-bold text-primary-900 mb-2 line-clamp-2 group-hover:text-accent-600 transition-colors"> 3D in-ear headphone icon </h3> <!-- Description --> <p class="text-primary-600 text-sm mb-4 line-clamp-2 leading-relaxed"> 3D Illustration in-ear headphone icon gives your project awesome illustration, this icon you can use for UI UX design, mobile apps, web infographics, and many more. </p> <!-- Footer --> <div class="flex items-center justify-between pt-4 border-t border-primary-100"> <div class="flex items-center gap-2 text-xs text-primary-500"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path> </svg> <span>Digital Download</span> </div> <a href="/products/3d-in-ear-headphone-icon" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
View Details →
</a> </div> </div> </div> </div> </div> <div class="text-center mt-12"> <a href="/products" class="btn-primary text-lg px-8 py-4">
View All Products
<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path> </svg> </a> </div> </div> </section>  <section class="py-16 bg-primary-50"> <div class="container"> <div class="text-center mb-12"> <h2 class="text-3xl md:text-4xl font-bold text-primary-900 mb-4">Why Choose Our Images?</h2> <p class="text-lg text-primary-600 max-w-2xl mx-auto">
Professional quality, instant access, and commercial licensing for all your creative projects
</p> </div> <div class="grid grid-cols-1 md:grid-cols-3 gap-8"> <div class="text-center p-8 bg-white rounded-3xl border border-primary-100 shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300"> <div class="w-16 h-16 bg-gradient-to-br from-accent-500 to-success-600 rounded-2xl flex items-center justify-center mx-auto mb-6"> <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path> </svg> </div> <h3 class="text-xl font-bold text-primary-900 mb-4">Premium Quality</h3> <p class="text-primary-600 leading-relaxed">Professional-grade digital images in ultra-high resolution for stunning results</p> </div> <div class="text-center p-8 bg-white rounded-3xl border border-primary-100 shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300"> <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-6"> <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path> </svg> </div> <h3 class="text-xl font-bold text-primary-900 mb-4">Instant Access</h3> <p class="text-primary-600 leading-relaxed">Download your images immediately after purchase with secure, lifetime access</p> </div> <div class="text-center p-8 bg-white rounded-3xl border border-primary-100 shadow-sm hover:shadow-xl hover:-translate-y-2 transition-all duration-300"> <div class="w-16 h-16 bg-gradient-to-br from-warning-500 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-6"> <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path> </svg> </div> <h3 class="text-xl font-bold text-primary-900 mb-4">Commercial License</h3> <p class="text-primary-600 leading-relaxed">Full commercial rights included - use for personal and business projects without limits</p> </div> </div> </div> </section>  </main> <footer class="bg-white border-t border-primary-100 py-12 text-primary-600"> <div class="container"> <div class="text-center"> <div class="flex items-center justify-center gap-2 text-lg font-semibold text-primary-900 mb-4"> <img src="/logo.svg" alt="Logo" loading="lazy" decoding="async" fetchpriority="auto" width="24" height="24" class="w-6 h-6 text-accent-600">
InfPik
</div> <div class="flex justify-center gap-4 mb-4"> <a href="/about" class="text-sm hover:text-accent-600 transition-colors">About Us</a> <a href="/privacy" class="text-sm hover:text-accent-600 transition-colors">Privacy Policy</a> <a href="/terms" class="text-sm hover:text-accent-600 transition-colors">Terms of Service</a> </div> <p class="text-sm">&copy; 2025 Polar Image Store. All rights reserved.</p> </div> </div> </footer> <script type="module">document.addEventListener("DOMContentLoaded",()=>{const o=document.getElementById("mobile-menu-button"),c=document.getElementById("mobile-menu");o&&c&&o.addEventListener("click",()=>{c.classList.toggle("hidden")});const i=document.getElementById("productSearch"),d=document.getElementById("mobileProductSearch"),e=document.getElementById("searchResults");let a;async function m(t){const s=t.value.trim();a&&clearTimeout(a),s.length>2?e&&(e.classList.remove("hidden"),e.innerHTML=`
            <div class="p-4 text-center text-primary-600">
              <div class="flex items-center justify-center gap-2">
                <svg class="animate-spin w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                <span class="text-sm">Searching for "${s}"...</span>
              </div>
            </div>
          `,a=setTimeout(async()=>{try{const r=await(await fetch(`/api/search?q=${encodeURIComponent(s)}`)).json();if(e&&!e.classList.contains("hidden"))if(r.results&&r.results.length>0){const u=r.results.map(n=>`
                    <a href="/products/${n.slug}" class="block p-3 hover:bg-primary-50 rounded-lg transition-colors border-b border-primary-100 last:border-b-0">
                      <div class="flex items-center gap-3">
                        ${n.image?`<img src="${n.image}" alt="${n.name}" class="w-10 h-10 object-cover rounded-lg">`:""}
                        <div class="flex-1 min-w-0">
                          <div class="text-primary-900 font-medium truncate">${n.name}</div>
                          <div class="text-primary-600 text-sm truncate">${n.description}</div>
                          <div class="text-accent-600 text-sm font-semibold">$${n.price}</div>
                        </div>
                      </div>
                    </a>
                  `).join("");e.innerHTML=`
                    <div class="p-2">
                      <div class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100">
                        Found ${r.total} result${r.total===1?"":"s"}
                      </div>
                      ${u}
                      <div class="p-3 border-t border-primary-100">
                        <a href="/products?search=${encodeURIComponent(s)}" class="block text-center text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                          View all ${r.total} results →
                        </a>
                      </div>
                    </div>
                  `}else e.innerHTML=`
                    <div class="p-4 text-center">
                      <div class="text-primary-600 mb-2">No results found for "${s}"</div>
                      <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors">
                        Browse all products →
                      </a>
                    </div>
                  `}catch(l){console.error("Search error:",l),e&&!e.classList.contains("hidden")&&(e.innerHTML=`
                  <div class="p-4 text-center text-red-600">
                    <div class="text-sm">Search failed. Please try again.</div>
                  </div>
                `)}},300)):e&&e.classList.add("hidden")}i&&(i.addEventListener("input",t=>m(t.target)),i.addEventListener("keydown",t=>{if(t.key==="Enter"){t.preventDefault();const s=t.target.value.trim();s&&(window.location.href=`/products?search=${encodeURIComponent(s)}`)}})),d&&d.addEventListener("keydown",t=>{if(t.key==="Enter"){t.preventDefault();const s=t.target.value.trim();s&&(window.location.href=`/products?search=${encodeURIComponent(s)}`)}}),document.addEventListener("click",t=>{e&&!i?.contains(t.target)&&!e.contains(t.target)&&e.classList.add("hidden")})});</script></body></html> <script type="module">document.addEventListener("DOMContentLoaded",()=>{window.addEventListener("categoryChange",e=>{const o=e.detail.categoryId;l(o)});function l(e){const o=document.querySelectorAll(".product-item");o.forEach(t=>{const r=t.dataset.category;parseInt(t.dataset.index),e==="all"||r===e?t.style.display="block":t.style.display="none"}),e==="all"&&o.forEach(t=>{parseInt(t.dataset.index)>=16&&(t.style.display="none")});const n=document.querySelector('a[href="/products"].btn-primary');if(n&&e!=="all"){const t=s(e);n.innerHTML=`
          View All ${t} Products
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
          </svg>
        `,n.href=`/products/category/${e}`}else n&&e==="all"&&(n.innerHTML=`
          View All Products
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
          </svg>
        `,n.href="/products")}function s(e){return e.split("-").map(o=>o.charAt(0).toUpperCase()+o.slice(1)).join(" ")}});</script>